---
import MainLayout from '@/layouts/MainLayout.astro';
import { SITE_CONFIG } from '@/config/site';
---

<MainLayout
  title="功能特性"
  description="探索玩机管家 (Android Device Management Tool) 强大功能，让Android设备管理变得简单高效"
>
  <!-- Hero Section -->
  <section class="bg-gradient-to-br from-primary-50 to-blue-100 dark:from-gray-900 dark:to-gray-800 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
        强大功能
      </h1>
      <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
        高效、安全、大规模管理Android设备所需的一切功能
      </p>
    </div>
  </section>

  <!-- Features Grid -->
  <section class="py-20 bg-white dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {SITE_CONFIG.features.map((feature) => (
          <div class="flex items-start space-x-4">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center text-2xl">
                {feature.icon}
              </div>
            </div>
            <div>
              <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                {feature.title}
              </h3>
              <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
                {feature.description}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  </section>

  <!-- Detailed Features -->
  <section class="py-20 bg-gray-50 dark:bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
          Why Choose DeviceManager Pro?
        </h2>
        <p class="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          Built for professionals who demand reliability, security, and performance
        </p>
      </div>

      <div class="space-y-20">
        <!-- Feature 1 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Enterprise-Grade Security
            </h3>
            <p class="text-gray-600 dark:text-gray-400 mb-6 leading-relaxed">
              Your data security is our top priority. All connections are encrypted with AES-256 encryption, 
              and we support enterprise authentication methods including SSO and LDAP integration.
            </p>
            <ul class="space-y-3">
              <li class="flex items-center text-gray-600 dark:text-gray-400">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                End-to-end encryption
              </li>
              <li class="flex items-center text-gray-600 dark:text-gray-400">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                Role-based access control
              </li>
              <li class="flex items-center text-gray-600 dark:text-gray-400">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                Audit logging
              </li>
            </ul>
          </div>
          <div class="bg-gradient-to-br from-primary-100 to-blue-100 dark:from-primary-900 dark:to-blue-900 rounded-2xl p-8 h-64 flex items-center justify-center">
            <div class="text-6xl">🔒</div>
          </div>
        </div>

        <!-- Feature 2 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div class="order-2 lg:order-1 bg-gradient-to-br from-green-100 to-emerald-100 dark:from-green-900 dark:to-emerald-900 rounded-2xl p-8 h-64 flex items-center justify-center">
            <div class="text-6xl">⚡</div>
          </div>
          <div class="order-1 lg:order-2">
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Lightning Fast Performance
            </h3>
            <p class="text-gray-600 dark:text-gray-400 mb-6 leading-relaxed">
              Optimized for speed and efficiency. Manage hundreds of devices simultaneously without 
              compromising on performance. Our advanced algorithms ensure minimal latency and maximum throughput.
            </p>
            <ul class="space-y-3">
              <li class="flex items-center text-gray-600 dark:text-gray-400">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                Sub-second response times
              </li>
              <li class="flex items-center text-gray-600 dark:text-gray-400">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                Concurrent device handling
              </li>
              <li class="flex items-center text-gray-600 dark:text-gray-400">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                Optimized resource usage
              </li>
            </ul>
          </div>
        </div>

        <!-- Feature 3 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Cross-Platform Compatibility
            </h3>
            <p class="text-gray-600 dark:text-gray-400 mb-6 leading-relaxed">
              Works seamlessly across Windows, macOS, and Linux. Native applications for each platform 
              ensure optimal performance and user experience while maintaining feature parity.
            </p>
            <div class="flex space-x-4">
              <div class="text-center">
                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-2">
                  <span class="text-2xl">🪟</span>
                </div>
                <span class="text-sm text-gray-600 dark:text-gray-400">Windows</span>
              </div>
              <div class="text-center">
                <div class="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center mb-2">
                  <span class="text-2xl">🍎</span>
                </div>
                <span class="text-sm text-gray-600 dark:text-gray-400">macOS</span>
              </div>
              <div class="text-center">
                <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center mb-2">
                  <span class="text-2xl">🐧</span>
                </div>
                <span class="text-sm text-gray-600 dark:text-gray-400">Linux</span>
              </div>
            </div>
          </div>
          <div class="bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900 dark:to-pink-900 rounded-2xl p-8 h-64 flex items-center justify-center">
            <div class="text-6xl">💻</div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="py-20 bg-primary-600 dark:bg-primary-800">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
        Experience All Features Today
      </h2>
      <p class="text-xl text-primary-100 mb-8">
        Start your free trial and see why professionals choose DeviceManager Pro
      </p>
      <a
        href="/download"
        class="bg-white text-primary-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
      >
        Download Free Trial
      </a>
    </div>
  </section>
</MainLayout>
