---
/**
 * 动态版本配置组件
 * 通过API获取DeviceManager Pro最新版本信息并更新下载配置
 */
import { deviceManagerVersionApi, type SoftwareVersionHistory, VersionApiService } from '@/services/versionApi';

// 尝试获取最新版本信息
let latestVersion: SoftwareVersionHistory | null = null;
let versionStats = null;
let dynamicDownloadConfig = null;
let errorMessage = '';

try {
  console.log('Fetching DeviceManager Pro version data...');

  // 获取最新版本
  latestVersion = await deviceManagerVersionApi.getLatestDeviceManagerVersion();

  if (latestVersion) {
    console.log('Successfully fetched DeviceManager Pro version:', latestVersion.version);

    // 只有在有版本数据时才获取统计和生成配置
    versionStats = await deviceManagerVersionApi.getDeviceManagerStats();
    dynamicDownloadConfig = await deviceManagerVersionApi.generateDynamicDownloadConfig();
  } else {
    console.warn('No version data available for DeviceManager Pro (Software ID: 6)');
    errorMessage = 'API暂无版本数据，请使用静态下载链接';
  }
} catch (error: any) {
  console.error('Failed to fetch DeviceManager Pro version data:', error);
  errorMessage = `版本信息获取失败: ${error?.message || '未知错误'}，请使用静态下载链接`;
}

// 使用从API获取的动态下载配置
// dynamicDownloadConfig 已经在上面通过 deviceManagerVersionApi.generateDynamicDownloadConfig() 生成
const dynamicDownloads = dynamicDownloadConfig;
---

<!-- 版本信息显示组件 -->
<div class="version-info-container" data-version-info>
  {latestVersion ? (
    <div class="version-success">
      <div class="version-badge">
        <span class="version-label">最新版本</span>
        <span class="version-number">{latestVersion.version}</span>
      </div>
      <div class="version-details">
        <span class="release-date">
          发布日期：{VersionApiService.formatReleaseDate(latestVersion.releaseDate)}
        </span>
        {latestVersion.fileSize && (
          <span class="file-size">
            文件大小：{latestVersion.fileSize}
          </span>
        )}
        {latestVersion.isStable && (
          <span class="stable-badge">稳定版</span>
        )}
      </div>
      {latestVersion.releaseNotes && (
        <div class="release-notes">
          <h4>更新说明：</h4>
          <p>{latestVersion.releaseNotes}</p>
        </div>
      )}
    </div>
  ) : (
    <div class="version-error">
      <span class="error-icon">⚠️</span>
      <span class="error-message">{errorMessage}</span>
    </div>
  )}
</div>

<!-- 动态下载配置数据 -->
<script type="application/json" id="dynamic-downloads-data" is:inline set:html={JSON.stringify(dynamicDownloads || null)}></script>

<!-- 版本信息数据 -->
<script type="application/json" id="version-info-data" is:inline set:html={JSON.stringify({
  latestVersion: latestVersion || null,
  versionStats: versionStats || null,
  errorMessage: errorMessage || ''
})}></script>

<style>
  .version-info-container {
    margin: 1rem 0;
    padding: 1rem;
    border-radius: 0.75rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #e2e8f0;
  }

  .version-success {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .version-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .version-label {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
  }

  .version-number {
    font-size: 1.125rem;
    font-weight: 700;
    color: #1e293b;
    background: #ffffff;
    padding: 0.25rem 0.75rem;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
  }

  .version-details {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
    font-size: 0.875rem;
    color: #64748b;
  }

  .stable-badge {
    background: #dcfce7;
    color: #166534;
    padding: 0.125rem 0.5rem;
    border-radius: 0.375rem;
    font-weight: 500;
    font-size: 0.75rem;
  }

  .release-notes {
    margin-top: 0.5rem;
    padding: 0.75rem;
    background: #ffffff;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
  }

  .release-notes h4 {
    margin: 0 0 0.5rem 0;
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
  }

  .release-notes p {
    margin: 0;
    font-size: 0.875rem;
    color: #6b7280;
    line-height: 1.5;
  }

  .version-error {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #dc2626;
    font-size: 0.875rem;
  }

  .version-stats {
    margin-top: 1rem;
    padding: 1rem;
    background: #ffffff;
    border-radius: 0.75rem;
    border: 1px solid #e2e8f0;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
  }

  .stat-item {
    text-align: center;
    padding: 0.75rem;
    background: #f8fafc;
    border-radius: 0.5rem;
  }

  .stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.25rem;
  }

  .stat-label {
    font-size: 0.75rem;
    color: #64748b;
    font-weight: 500;
  }

  /* 深色模式支持 */
  :global(.dark) .version-info-container {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border-color: #475569;
  }

  :global(.dark) .version-number {
    background: #334155;
    color: #f1f5f9;
    border-color: #475569;
  }

  :global(.dark) .release-notes {
    background: #334155;
    border-color: #475569;
  }

  :global(.dark) .release-notes h4 {
    color: #e2e8f0;
  }

  :global(.dark) .release-notes p {
    color: #cbd5e1;
  }

  :global(.dark) .version-stats {
    background: #334155;
    border-color: #475569;
  }

  :global(.dark) .stat-item {
    background: #475569;
  }

  :global(.dark) .stat-value {
    color: #f1f5f9;
  }

  :global(.dark) .stat-label {
    color: #cbd5e1;
  }
</style>
