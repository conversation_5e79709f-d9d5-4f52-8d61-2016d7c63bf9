---
import { SITE_CONFIG } from '@/config/site';

export interface Props {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  noindex?: boolean;
}

const {
  title = SITE_CONFIG.name,
  description = SITE_CONFIG.description,
  keywords = SITE_CONFIG.keywords,
  image = '/og-image.jpg',
  noindex = false,
} = Astro.props;

const canonicalURL = new URL(Astro.url.pathname, Astro.site);
const fullTitle = title === SITE_CONFIG.name ? title : `${title} | ${SITE_CONFIG.name}`;
---

<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/png" href="/favicon.png" />
    <link rel="apple-touch-icon" href="/favicon.png" />
    <meta name="generator" content={Astro.generator} />
    
    <!-- Primary Meta Tags -->
    <title>{fullTitle}</title>
    <meta name="title" content={fullTitle} />
    <meta name="description" content={description} />
    <meta name="keywords" content={keywords.join(', ')} />
    <meta name="author" content={SITE_CONFIG.author} />
    <link rel="canonical" href={canonicalURL} />
    
    <!-- Robots -->
    {noindex && <meta name="robots" content="noindex, nofollow" />}
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content={canonicalURL} />
    <meta property="og:title" content={fullTitle} />
    <meta property="og:description" content={description} />
    <meta property="og:image" content={new URL(image, Astro.url)} />
    <meta property="og:site_name" content={SITE_CONFIG.name} />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content={canonicalURL} />
    <meta property="twitter:title" content={fullTitle} />
    <meta property="twitter:description" content={description} />
    <meta property="twitter:image" content={new URL(image, Astro.url)} />
    <meta property="twitter:site" content={SITE_CONFIG.social.twitter} />
    
    <!-- Structured Data -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": SITE_CONFIG.name,
        "description": SITE_CONFIG.description,
        "url": SITE_CONFIG.url,
        "applicationCategory": "DeveloperApplication",
        "operatingSystem": SITE_CONFIG.product.platforms.join(", "),
        "softwareVersion": SITE_CONFIG.product.version,
        "datePublished": SITE_CONFIG.product.releaseDate,
        "author": {
          "@type": "Organization",
          "name": SITE_CONFIG.author
        },
        "offers": {
          "@type": "Offer",
          "price": "29",
          "priceCurrency": "USD"
        }
      }
    </script>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    
    <!-- Theme Script -->
    <script is:inline>
      function getThemePreference() {
        if (typeof localStorage !== 'undefined' && localStorage.getItem('theme')) {
          return localStorage.getItem('theme');
        }
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      }
      
      const isDark = getThemePreference() === 'dark';
      document.documentElement.classList[isDark ? 'add' : 'remove']('dark');
      
      if (typeof localStorage !== 'undefined') {
        const observer = new MutationObserver(() => {
          const isDark = document.documentElement.classList.contains('dark');
          localStorage.setItem('theme', isDark ? 'dark' : 'light');
        });
        observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] });
      }
    </script>
  </head>
  <body class="bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300">
    <slot />

    <!-- Speed Insights -->
    <script>
      import { injectSpeedInsights } from '@vercel/speed-insights';
      injectSpeedInsights();
    </script>
  </body>
</html>
