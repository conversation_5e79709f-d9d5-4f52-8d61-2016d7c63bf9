---
import BaseLayout from './BaseLayout.astro';
import Header from '@/components/Header.astro';
import Footer from '@/components/Footer.astro';

export interface Props {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  noindex?: boolean;
}

const props = Astro.props;
---

<BaseLayout {...props}>
  <div class="min-h-screen flex flex-col">
    <Header />
    <main class="flex-grow pt-24">
      <slot />
    </main>
    <Footer />
  </div>
</BaseLayout>
