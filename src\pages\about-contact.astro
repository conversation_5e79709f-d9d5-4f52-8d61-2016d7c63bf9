---
import MainLayout from '@/layouts/MainLayout.astro';
import { SITE_CONFIG } from '@/config/site';
---

<MainLayout
  title="关于与联系"
  description="了解玩机管家 (Android Device Management Tool) 团队和我们的使命，以及如何与我们取得联系。"
>
  <!-- Hero Section -->
  <section class="bg-gradient-to-br from-primary-50 to-blue-100 dark:from-gray-900 dark:to-gray-800 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
        关于与联系
      </h1>
      <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
        了解玩机管家背后的团队，以及如何与我们取得联系
      </p>
    </div>
  </section>

  <!-- About Section -->
  <section class="py-20 bg-white dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
          关于我们
        </h2>
        <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          我们致力于让Android设备管理变得简单、安全、高效
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-20">
        <div>
          <h3 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-6">
            我们的故事
          </h3>
          <div class="space-y-4 text-gray-600 dark:text-gray-400 leading-relaxed">
            <p>
              玩机管家诞生于对现有工具的不满。作为移动开发者和IT专业人士，
              我们厌倦了在多个工具间切换，处理不稳定的连接，以及在本应几分钟完成的任务上花费数小时。
            </p>
            <p>
              2020年，我们的团队汇聚了经验丰富的开发者和系统管理员，
              目标很简单：创造市场上最直观、最强大的Android设备管理工具。
            </p>
            <p>
              如今，玩机管家已被50多个国家的超过10,000名专业人士信赖，
              从独立开发者到世界500强企业。我们很自豪能成为他们开发流程的一部分，
              帮助他们更快地发布更好的应用。
            </p>
          </div>
        </div>
        <div class="bg-gradient-to-br from-primary-100 to-blue-100 dark:from-primary-900 dark:to-blue-900 rounded-2xl p-8 h-96 flex items-center justify-center">
          <div class="text-8xl">🚀</div>
        </div>
      </div>

      <!-- Team Values -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-20">
        <div class="text-center p-6">
          <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
            <span class="text-2xl">💡</span>
          </div>
          <h4 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">创新</h4>
          <p class="text-gray-600 dark:text-gray-400">
            持续推动技术边界，为用户带来最前沿的解决方案
          </p>
        </div>
        <div class="text-center p-6">
          <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
            <span class="text-2xl">🤝</span>
          </div>
          <h4 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">用户至上</h4>
          <p class="text-gray-600 dark:text-gray-400">
            倾听用户反馈，以用户需求为产品发展的核心驱动力
          </p>
        </div>
        <div class="text-center p-6">
          <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
            <span class="text-2xl">🔒</span>
          </div>
          <h4 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">安全可靠</h4>
          <p class="text-gray-600 dark:text-gray-400">
            确保数据安全和隐私保护是我们产品设计的首要考虑
          </p>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact Section -->
  <section class="py-20 bg-gray-50 dark:bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
          联系我们
        </h2>
        <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          有问题？需要支持？想要分享反馈？我们很乐意听到您的声音
        </p>
      </div>

      <!-- Contact Options -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
        <!-- Email Support -->
        <div class="text-center p-6 bg-white dark:bg-gray-900 rounded-xl shadow-sm">
          <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            邮件支持
          </h3>
          <p class="text-gray-600 dark:text-gray-400 mb-4">
            获取技术问题和一般问题的帮助
          </p>
          <a
            href={`mailto:${SITE_CONFIG.social.email}`}
            class="text-primary-600 dark:text-primary-400 font-semibold hover:text-primary-700 dark:hover:text-primary-300"
          >
            {SITE_CONFIG.social.email}
          </a>
          <p class="text-sm text-gray-500 dark:text-gray-500 mt-2">
            24小时内回复
          </p>
        </div>

        <!-- GitHub -->
        <div class="text-center p-6 bg-white dark:bg-gray-900 rounded-xl shadow-sm">
          <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-primary-600 dark:text-primary-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            GitHub
          </h3>
          <p class="text-gray-600 dark:text-gray-400 mb-4">
            查看源代码、报告问题或贡献代码
          </p>
          <a
            href={SITE_CONFIG.social.github}
            target="_blank"
            rel="noopener noreferrer"
            class="text-primary-600 dark:text-primary-400 font-semibold hover:text-primary-700 dark:hover:text-primary-300"
          >
            访问GitHub
          </a>
          <p class="text-sm text-gray-500 dark:text-gray-500 mt-2">
            开源社区
          </p>
        </div>

        <!-- Social Media -->
        <div class="text-center p-6 bg-white dark:bg-gray-900 rounded-xl shadow-sm">
          <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-primary-600 dark:text-primary-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            社交媒体
          </h3>
          <p class="text-gray-600 dark:text-gray-400 mb-4">
            关注我们获取最新动态和技巧
          </p>
          <a
            href={`https://twitter.com/${SITE_CONFIG.social.twitter.replace('@', '')}`}
            target="_blank"
            rel="noopener noreferrer"
            class="text-primary-600 dark:text-primary-400 font-semibold hover:text-primary-700 dark:hover:text-primary-300"
          >
            {SITE_CONFIG.social.twitter}
          </a>
          <p class="text-sm text-gray-500 dark:text-gray-500 mt-2">
            实时更新
          </p>
        </div>
      </div>

      <!-- FAQ Section -->
      <div class="bg-white dark:bg-gray-900 rounded-2xl p-8">
        <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center">
          常见问题
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              如何获取技术支持？
            </h4>
            <p class="text-gray-600 dark:text-gray-400">
              您可以通过邮件联系我们的技术支持团队，我们会在24小时内回复。对于紧急问题，请在邮件标题中标注"紧急"。
            </p>
          </div>
          <div>
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              是否提供定制化服务？
            </h4>
            <p class="text-gray-600 dark:text-gray-400">
              是的，我们为企业客户提供定制化解决方案。请通过邮件联系我们，详细说明您的需求。
            </p>
          </div>
          <div>
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              如何报告Bug或提出功能建议？
            </h4>
            <p class="text-gray-600 dark:text-gray-400">
              您可以在我们的GitHub仓库中创建Issue，或者通过邮件向我们反馈。我们非常重视用户的反馈。
            </p>
          </div>
          <div>
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              是否有用户社区？
            </h4>
            <p class="text-gray-600 dark:text-gray-400">
              我们正在建设用户社区，您可以关注我们的社交媒体获取最新信息，或加入我们的GitHub讨论区。
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>
</MainLayout>
